25-07-31.10:12:54.251 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 23.0.2 with PID 12152 (started by xxw in /Users/<USER>/Desktop/study/xfg/ai-agent-station-study/ai-agent-station-study-app)
25-07-31.10:12:54.252 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-07-31.10:12:54.643 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.bugstack.ai]' package. Please check your configuration.
25-07-31.10:12:54.851 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-07-31.10:12:55.216 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-07-31.10:12:55.218 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-07-31.10:12:55.218 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-31.10:12:55.237 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 1.272 seconds (process running for 1.827)
25-07-31.10:12:57.461 [main            ] INFO  OpenAiTest             - 测试结果(call):{"metadata":{"empty":false,"id":"chatcmpl-BzD4u206AGhv6PXP7eQQhK26NQQnn","model":"gpt-4o","rateLimit":{"requestsLimit":11410,"requestsRemaining":11401,"tokensLimit":11410000,"tokensRemaining":11381387},"usage":{"promptTokens":10,"completionTokens":8,"totalTokens":18,"nativeUsage":{"completion_tokens":8,"completion_tokens_details":{"accepted_prediction_tokens":0,"audio_tokens":0,"reasoning_tokens":0,"rejected_prediction_tokens":0},"prompt_tokens":10,"prompt_tokens_details":{"audio_tokens":0,"cached_tokens":0},"total_tokens":18}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","annotations":[],"index":0,"id":"chatcmpl-BzD4u206AGhv6PXP7eQQhK26NQQnn"},"text":"1 + 1 = 2","toolCalls":[]}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.completion_tokens_details.prompt_tokens_details.result"}]}
25-07-31.10:14:12.038 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 23.0.2 with PID 12189 (started by xxw in /Users/<USER>/Desktop/study/xfg/ai-agent-station-study/ai-agent-station-study-app)
25-07-31.10:14:12.038 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-07-31.10:14:12.333 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.bugstack.ai]' package. Please check your configuration.
25-07-31.10:14:12.520 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-07-31.10:14:12.882 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-07-31.10:14:12.884 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-07-31.10:14:12.884 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-31.10:14:12.904 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 1.049 seconds (process running for 1.616)
25-07-31.10:14:17.910 [main            ] INFO  OpenAiTest             - 测试结果(images):{"metadata":{"empty":false,"id":"chatcmpl-BzD6B6hqc2CsOgPmGiIssx2B4VSpr","model":"gpt-4o","rateLimit":{"requestsLimit":11410,"requestsRemaining":11402,"tokensLimit":11410000,"tokensRemaining":11315853},"usage":{"promptTokens":280,"completionTokens":172,"totalTokens":452,"nativeUsage":{"completion_tokens":172,"completion_tokens_details":{"accepted_prediction_tokens":0,"audio_tokens":0,"reasoning_tokens":0,"rejected_prediction_tokens":0},"prompt_tokens":280,"prompt_tokens_details":{"audio_tokens":0,"cached_tokens":0},"total_tokens":452}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","finishReason":"STOP","refusal":"","annotations":[],"index":0,"id":"chatcmpl-BzD6B6hqc2CsOgPmGiIssx2B4VSpr"},"text":"这是一张卡通风格的图片，主要内容是一个棕色的小狗形象。小狗有大大的眼睛，显得有点惊讶或困惑。它的身体是棕色，耳朵下垂，尾巴翘起，整体造型非常可爱。\n\n这种卡通图像可能用于以下用途：\n1. **表情包或聊天表情**：表达惊讶、困惑或其他情绪。\n2. **儿童绘本或教育材料**：吸引孩子的注意力，作为插图使用。\n3. **品牌或产品标志**：例如宠物用品店、宠物食品或相关服务的吉祥物。\n4. **装饰**：用于网站设计、包装设计或其他创意项目。","toolCalls":[]}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.completion_tokens_details.prompt_tokens_details.result"}]}
25-07-31.10:15:06.712 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 23.0.2 with PID 12198 (started by xxw in /Users/<USER>/Desktop/study/xfg/ai-agent-station-study/ai-agent-station-study-app)
25-07-31.10:15:06.713 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-07-31.10:15:07.157 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.bugstack.ai]' package. Please check your configuration.
25-07-31.10:15:07.375 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-07-31.10:15:07.764 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-07-31.10:15:07.767 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-07-31.10:15:07.767 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-31.10:15:07.792 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 1.299 seconds (process running for 1.848)
25-07-31.10:15:13.127 [ForkJoinPool.commonPool-worker-1] ERROR MessageAggregator      - Aggregation Error
org.springframework.web.reactive.function.client.WebClientRequestException: Remote host terminated the handshake
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to POST https://yibuapi.com/v1/chat/completions [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:978)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:955)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:554)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:658)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:888)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:527)
		at java.base/java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:507)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1460)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:2036)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:189)
Caused by: javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
	at java.net.http/jdk.internal.net.http.common.SSLTube.checkForHandshake(SSLTube.java:595)
	at java.net.http/jdk.internal.net.http.common.SSLTube$SSLSubscriberWrapper.onComplete(SSLTube.java:536)
	at java.net.http/jdk.internal.net.http.common.SubscriberWrapper.checkCompletion(SubscriberWrapper.java:474)
	at java.net.http/jdk.internal.net.http.common.SubscriberWrapper$DownstreamPusher.run1(SubscriberWrapper.java:334)
	at java.net.http/jdk.internal.net.http.common.SubscriberWrapper$DownstreamPusher.run(SubscriberWrapper.java:259)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:182)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:207)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:280)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:233)
	at java.net.http/jdk.internal.net.http.common.SubscriberWrapper.outgoing(SubscriberWrapper.java:232)
	at java.net.http/jdk.internal.net.http.common.SSLFlowDelegate$Reader.processData(SSLFlowDelegate.java:540)
	at java.net.http/jdk.internal.net.http.common.SSLFlowDelegate$Reader$ReaderDownstreamPusher.run(SSLFlowDelegate.java:283)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:182)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:207)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
25-07-31.10:16:04.215 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 23.0.2 with PID 12215 (started by xxw in /Users/<USER>/Desktop/study/xfg/ai-agent-station-study/ai-agent-station-study-app)
25-07-31.10:16:04.216 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-07-31.10:16:04.507 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.bugstack.ai]' package. Please check your configuration.
25-07-31.10:16:04.696 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-07-31.10:16:05.112 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-07-31.10:16:05.113 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-07-31.10:16:05.113 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-31.10:16:05.133 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 1.132 seconds (process running for 1.802)
25-07-31.10:16:28.187 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 23.0.2 with PID 12226 (started by xxw in /Users/<USER>/Desktop/study/xfg/ai-agent-station-study/ai-agent-station-study-app)
25-07-31.10:16:28.188 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-07-31.10:16:28.474 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.bugstack.ai]' package. Please check your configuration.
25-07-31.10:16:28.657 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-07-31.10:16:28.998 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-07-31.10:16:29.002 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-07-31.10:16:29.002 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-31.10:16:29.026 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 0.994 seconds (process running for 1.471)
25-07-31.10:17:17.563 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 23.0.2 with PID 12232 (started by xxw in /Users/<USER>/Desktop/study/xfg/ai-agent-station-study/ai-agent-station-study-app)
25-07-31.10:17:17.563 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-07-31.10:17:17.837 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.bugstack.ai]' package. Please check your configuration.
25-07-31.10:17:18.019 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-07-31.10:17:18.338 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-07-31.10:17:18.339 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-07-31.10:17:18.339 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-31.10:17:18.375 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 1.1 seconds (process running for 1.694)
25-07-31.10:17:48.853 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 23.0.2 with PID 12241 (started by xxw in /Users/<USER>/Desktop/study/xfg/ai-agent-station-study/ai-agent-station-study-app)
25-07-31.10:17:48.853 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-07-31.10:17:49.136 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.bugstack.ai]' package. Please check your configuration.
25-07-31.10:17:49.315 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-07-31.10:17:49.660 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-07-31.10:17:49.675 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-07-31.10:17:49.675 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-31.10:17:49.694 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 1.02 seconds (process running for 1.552)
25-07-31.10:17:50.240 [main            ] INFO  TextSplitter           - Splitting up document into 2 chunks.
25-07-31.10:17:54.542 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Starting...
25-07-31.10:17:54.657 [main            ] INFO  HikariPool             - Retail_HikariCP - Added connection org.postgresql.jdbc.PgConnection@72d1334f
25-07-31.10:17:54.658 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Start completed.
25-07-31.10:17:54.733 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown initiated...
25-07-31.10:17:54.735 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown completed.
25-07-31.10:42:30.609 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 23.0.2 with PID 12920 (started by xxw in /Users/<USER>/Desktop/study/xfg/ai-agent-station-study/ai-agent-station-study-app)
25-07-31.10:42:30.610 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-07-31.10:42:31.059 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.bugstack.ai]' package. Please check your configuration.
25-07-31.10:42:31.281 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-07-31.10:42:31.700 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-07-31.10:42:31.703 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-07-31.10:42:31.703 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-31.10:42:31.744 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 1.378 seconds (process running for 2.028)
25-07-31.10:42:32.392 [main            ] INFO  TextSplitter           - Splitting up document into 2 chunks.
25-07-31.10:42:36.482 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Starting...
25-07-31.10:42:36.576 [main            ] INFO  HikariPool             - Retail_HikariCP - Added connection org.postgresql.jdbc.PgConnection@72d1334f
25-07-31.10:42:36.577 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Start completed.
25-07-31.10:42:36.644 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown initiated...
25-07-31.10:42:36.645 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown completed.
25-07-31.10:44:09.683 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 23.0.2 with PID 12950 (started by xxw in /Users/<USER>/Desktop/study/xfg/ai-agent-station-study/ai-agent-station-study-app)
25-07-31.10:44:09.684 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-07-31.10:44:10.181 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.bugstack.ai]' package. Please check your configuration.
25-07-31.10:44:10.477 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-07-31.10:44:10.980 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-07-31.10:44:10.985 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-07-31.10:44:10.985 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-31.10:44:11.039 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 1.599 seconds (process running for 2.39)
25-07-31.10:44:11.750 [main            ] INFO  TextSplitter           - Splitting up document into 2 chunks.
25-07-31.10:44:15.178 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Starting...
25-07-31.10:44:15.265 [main            ] INFO  HikariPool             - Retail_HikariCP - Added connection org.postgresql.jdbc.PgConnection@63a6ca5a
25-07-31.10:44:15.265 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Start completed.
25-07-31.10:44:15.333 [main            ] INFO  OpenAiTest             - 上传完成
25-07-31.10:44:15.340 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown initiated...
25-07-31.10:44:15.341 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown completed.
25-07-31.10:48:23.763 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 23.0.2 with PID 13007 (started by xxw in /Users/<USER>/Desktop/study/xfg/ai-agent-station-study/ai-agent-station-study-app)
25-07-31.10:48:23.763 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-07-31.10:48:24.303 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.bugstack.ai]' package. Please check your configuration.
25-07-31.10:48:24.591 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-07-31.10:48:25.122 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-07-31.10:48:25.124 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-07-31.10:48:25.124 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-31.10:48:25.150 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 1.712 seconds (process running for 2.413)
25-07-31.10:48:29.235 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Starting...
25-07-31.10:48:29.335 [main            ] INFO  HikariPool             - Retail_HikariCP - Added connection org.postgresql.jdbc.PgConnection@19941314
25-07-31.10:48:29.336 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Start completed.
25-07-31.10:48:29.418 [main            ] INFO  OpenAiTest             - 上传完成
25-07-31.10:48:29.433 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown initiated...
25-07-31.10:48:29.435 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown completed.
25-07-31.10:48:50.356 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 23.0.2 with PID 13028 (started by xxw in /Users/<USER>/Desktop/study/xfg/ai-agent-station-study/ai-agent-station-study-app)
25-07-31.10:48:50.357 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-07-31.10:48:50.841 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.bugstack.ai]' package. Please check your configuration.
25-07-31.10:48:51.158 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-07-31.10:48:51.613 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-07-31.10:48:51.615 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-07-31.10:48:51.615 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-31.10:48:51.638 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 1.52 seconds (process running for 2.123)
25-07-31.10:48:55.921 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Starting...
25-07-31.10:48:56.040 [main            ] INFO  HikariPool             - Retail_HikariCP - Added connection org.postgresql.jdbc.PgConnection@2919d85c
25-07-31.10:48:56.041 [main            ] INFO  HikariDataSource       - Retail_HikariCP - Start completed.
25-07-31.10:48:57.753 [main            ] INFO  OpenAiTest             - 测试结果:{"metadata":{"empty":false,"id":"chatcmpl-BzDdlAx07gHEdboeYsq0N0msrGsjc","model":"gpt-4o-2024-11-20","rateLimit":{"requestsLimit":299900,"requestsRemaining":299494,"tokensLimit":29990000,"tokensRemaining":29566856},"usage":{"promptTokens":94,"completionTokens":24,"totalTokens":118,"nativeUsage":{"completion_tokens":24,"completion_tokens_details":{"accepted_prediction_tokens":0,"audio_tokens":0,"reasoning_tokens":0,"rejected_prediction_tokens":0},"prompt_tokens":94,"prompt_tokens_details":{"audio_tokens":0,"cached_tokens":0},"total_tokens":118}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"role":"ASSISTANT","messageType":"ASSISTANT","refusal":"","finishReason":"STOP","index":0,"annotations":[],"id":"chatcmpl-BzDdlAx07gHEdboeYsq0N0msrGsjc"},"text":"根据提供的信息，王大瓜出生于1993年。因此，到2023年，他应该是30岁。","toolCalls":[]}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.completion_tokens_details.prompt_tokens_details.result"}]}
25-07-31.10:48:57.760 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown initiated...
25-07-31.10:48:57.761 [SpringApplicationShutdownHook] INFO  HikariDataSource       - Retail_HikariCP - Shutdown completed.
25-07-31.10:55:52.274 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 23.0.2 with PID 13150 (started by xxw in /Users/<USER>/Desktop/study/xfg/ai-agent-station-study/ai-agent-station-study-app)
25-07-31.10:55:52.275 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-07-31.10:55:52.686 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.bugstack.ai]' package. Please check your configuration.
25-07-31.10:55:52.933 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-07-31.10:55:53.365 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store. Is empty: false
25-07-31.10:55:53.368 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store in schema: public
25-07-31.10:55:53.368 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-31.10:55:53.395 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 1.34 seconds (process running for 2.044)
