<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bugstack.ai.infrastructure.persistent.dao.Xxx">

    <resultMap id="CaseMap" type="cn.bugstack.ai.infrastructure.persistent.po.A">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="insert" parameterType="cn.bugstack.ai.infrastructure.persistent.po.A">
        INSERT INTO table(a,b,c) VALUES(#{a}, #{b}, #{c})
    </insert>

    <update id="update" parameterType="cn.bugstack.ai.infrastructure.persistent.po.A">
        UPDATE table SET a = #{a} WHERE b = #{b}
    </update>

    <select id="queryEmployeeByEmployNumber" parameterType="java.lang.String" resultMap="CaseMap">
        SELECT a, b, c
        FROM table
        WHERE a = #{a}
    </select>

</mapper>
