# /usr/local/bin/docker-compose -f /docs/dev-ops/environment/environment-docker-compose-2.4.yml up -d
version: '3.8'
# docker-compose -f docker-compose-app.yml up -d
# 你需要修改system为你自身系统的仓库名
services:
  # MCP 服务，CSDN 发帖，注意 CSDN_API_COOKIE 修改为你的。
  mcp-server-csdn-app:
    # 课程代码：https://gitcode.net/KnowledgePlanet/mcp-server-csdn
    #    image: fuzhengwei/mcp-server-csdn-app:1.1
    image: registry.cn-hangzhou.aliyuncs.com/fuzhengwei/mcp-server-csdn-app:1.1
    container_name: mcp-server-csdn-app
    restart: always
    ports:
      - "8101:8101"
    volumes:
      - ./log:/data/log
    environment:
      - TZ=PRC
      - SERVER_PORT=8101
      - CSDN_API_CATEGORIES=Java场景面试宝典
      - CSDN_API_COOKIE=uuid_tt_dd=10_37460597350-1744844879196-503换成你的cookie
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - my-network

  # 课程代码：https://gitcode.net/KnowledgePlanet/mcp-server-weixin
  # MCP 服务，微信公众号通知，注意 environment 下的微信配置修改为你的 https://mp.weixin.qq.com/debug/cgi-bin/sandboxinfo?action=showinfo&t=sandbox/index
  mcp-server-weixin-app:
    #    image: fuzhengwei/mcp-server-weixin-app:1.1
    image: registry.cn-hangzhou.aliyuncs.com/fuzhengwei/mcp-server-weixin-app:1.1
    container_name: mcp-server-weixin-app
    restart: always
    ports:
      - "8102:8102"
    volumes:
      - ./log:/data/log
    environment:
      - TZ=PRC
      - SERVER_PORT=8102
      - WEIXIN_API_ORIGINAL_ID=gh_e067c267e056
      - WEIXIN_API_APP_ID=wx5a228ff69e28a91f
      - WEIXIN_API_APP_SECRET=0bea03aa1310bac050aae79dd8703928
      - WEIXIN_API_TEMPLATE_ID=O8qI6gy75F-bXfPiQugInTMLA0MRzaMff9WSBb16cFk
      - WEIXIN_API_TOUSER=or0Ab6ivwmypESVp_bYuk92T6SvU
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - my-network

networks:
  my-network:
    driver: bridge
