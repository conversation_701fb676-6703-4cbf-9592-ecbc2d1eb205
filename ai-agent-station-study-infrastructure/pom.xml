<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.bugstack.ai</groupId>
        <artifactId>ai-agent-station-study</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>ai-agent-station-study-infrastructure</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- 系统模块 -->
        <dependency>
            <groupId>cn.bugstack.ai</groupId>
            <artifactId>ai-agent-station-study-domain</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>ai-agent-station-study-infrastructure</finalName>
    </build>

</project>
