<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.bugstack.ai</groupId>
        <artifactId>ai-agent-station-study</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>ai-agent-station-study-trigger</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!-- 系统模块 -->
        <dependency>
            <groupId>cn.bugstack.ai</groupId>
            <artifactId>ai-agent-station-study-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.bugstack.ai</groupId>
            <artifactId>ai-agent-station-study-types</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.bugstack.ai</groupId>
            <artifactId>ai-agent-station-study-domain</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>ai-agent-station-study-trigger</finalName>
    </build>

</project>
